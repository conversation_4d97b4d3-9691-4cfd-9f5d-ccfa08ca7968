<!-- Create Tab Content -->
<div class="create-tab-container">

    <!-- Sidebar - New Fields Panel -->
    <div class="fields-sidebar" #sidebar>
        <div class="relative h-100 p-3">
            <!-- Field Type Selector -->
            <app-field-type-selector [availableFieldTypes]="configStateService.getCreateTabAvailableFieldTypes()">
            </app-field-type-selector>

            <div class="new-section-container">
                <button mat-raised-button color="primary" class="new-section-btn" (click)="onAddSection()" type="button">
          
          <!-- Icon -->
          <mat-icon class="btn-icon">add</mat-icon>
          
          <!-- Text -->
          <span class="btn-text">
            {{ 'DYNAMIC_LAYOUT_BUILDER.NEW_FIELDS.NEW_SECTION' | translate }}
          </span>
        </button>

                <!-- Mô tả -->
                <p class="new-section-description">
                    {{ 'DYNAMIC_LAYOUT_BUILDER.NEW_SECTION.DESCRIPTION' | translate }}
                </p>
            </div>


            <div class="panel-resize-handle resize-handle--absolute" appResizePanel [leftPanel]="sidebar" [rightPanel]="rightPanel" [minWidth]="400" [maxWidth]="600" [showLeftPanel]="true" [additionalMarginLeft]="0" [isAbsolute]="true" [dragButtonBottom]="'40px'"
                [panelName]="'dynamic-layout'"></div>
        </div>
    </div>

    <!-- Main Layout Area -->
    <div class="layout-main-content" #rightPanel>

        <!-- Loading Indicator -->
        <div *ngIf="isLoading()" class="loading-container">
            <mat-spinner></mat-spinner>
            <p>{{ 'DYNAMIC_LAYOUT_BUILDER.LOADING' | translate }}</p>
        </div>

        <!-- Empty State -->
        <div *ngIf="!hasAnySections() && !isLoading()" class="empty-state">
            <mat-icon class="empty-icon">dashboard</mat-icon>
            <h3>{{ 'DYNAMIC_LAYOUT_BUILDER.EMPTY_STATE.NO_SECTIONS' | translate }}</h3>
            <p>{{ 'DYNAMIC_LAYOUT_BUILDER.EMPTY_STATE.NO_SECTIONS_DESCRIPTION' | translate }}</p>
        </div>

        <!-- Sections Container -->
        <div *ngIf="hasAnySections() && !isLoading()" class="sections-container" cdkDropList [cdkDropListData]="configStateService.createTabSections()">
            <!-- Section Components -->
            <app-section *ngFor="let section of configStateService.createTabSections(); trackBy: trackBySection" [section]="section" (sectionDeleted)="onSectionDeleted($event)" (sectionTitleChanged)="onSectionTitleChanged($event)" (fieldsReordered)="onSectionFieldsReordered($event)"
                (fieldRequiredToggled)="onSectionFieldRequiredToggled($event)" (fieldPropertiesEdit)="onSectionFieldPropertiesEdit($event)" (fieldPermissionSet)="onSectionFieldPermissionSet($event)" (fieldDeleted)="onSectionFieldDeleted($event)" (fieldLabelChanged)="onSectionFieldLabelChanged($event)"
                (quickAddField)="onSectionQuickAddField($event)">
            </app-section>
        </div>

        <!-- Preview Panel -->
        <div *ngIf="isPreviewMode()" class="preview-panel">
            <app-preview-panel [sections]="configStateService.createTabSections">
            </app-preview-panel>
        </div>

    </div>

</div>