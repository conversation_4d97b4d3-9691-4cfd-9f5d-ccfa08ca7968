
/**
 * Interface cho profile v<PERSON><PERSON> quyền truy cập field
 */
export interface FieldPermissionProfile {
  _id: string;
  name: string;
  permission: 'read_write' | 'read' | 'none';
}

/**
 * Interface định nghĩa các loại field type có thể sử dụng trong hệ thống
 * Sử dụng union type để đảm bảo type safety
 */
export type FieldType =
  // Trường cơ bản
  | 'text'
  | 'number'
  | 'email'
  | 'phone'
  | 'textarea'
  | 'date'
  | 'datetime'
  | 'file'
  | 'image'
  | 'checkbox'
  | 'radio'
  | 'select'
  // Trường nâng cao
  | 'picklist'
  | 'multi-picklist'
  | 'url'
  | 'decimal'
  | 'currency'
  | 'percent'
  | 'search'
  | 'user'
  ;

/**
 * Type cho field value - có thể là string, number, boolean, array, hoặc null
 */
export type FieldValue = string | number | boolean | Date | string[] | number[] | null | undefined;

/**
 * Type cho field constraints - định nghĩa các ràng buộc cho field
 */
export interface FieldConstraints {
  // Text field constraints
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  textType?: string; // 'small', 'medium', 'large'

  // Number field constraints
  min?: number;
  max?: number;
  step?: number;
  maxDigits?: number;

  // Date field constraints
  minDate?: string;
  maxDate?: string;

  // Select/dropdown constraints
  options?: Array<{ value: string | number; label: string }> | string[];
  picklistValues?: string[]; // For picklist fields
  multiple?: boolean;

  // File upload constraints
  allowedFileTypes?: string[];
  allowedTypes?: string[]; // Alias for allowedFileTypes
  maxFileSize?: number; // in bytes
  maxSize?: number; // Alias for maxFileSize
  allowMultipleFiles?: boolean;
  maxFiles?: number;
  maxImages?: number;

  // Checkbox/Radio constraints
  defaultValue?: boolean | string | number | string[];
  enableByDefault?: boolean;

  // Color field constraints
  format?: string; // 'hex', 'rgb', 'hsl'

  // Search/Brand constraints
  searchable?: boolean;
  hierarchical?: boolean;

  // Email constraints
  unique?: boolean;

  // Permission constraints
  permissions?: Array<{ profileId: string; permission: string }>;

  // Search module constraints
  searchModule?: string;

  // Picklist constraints
  sortOrder?: 'input' | 'alphabetical';

  // User field constraints
  userType?: 'single' | 'multiple';

  // Common constraints
  required?: boolean;
  readonly?: boolean;
  disabled?: boolean;
}
/**
 * Type cho field default value
 */
export type FieldDefaultValue = string | number | boolean | string[] | null | undefined;


/**
 * Interface cho field option object
 */
export interface FieldOption {
  label: string;
  value: string | number | boolean;
}

/**
 * Union type cho field options - có thể là array string hoặc array object
 */
export type FieldOptions = string[] | FieldOption[];

export interface BaseField {
  /**
   * mongo _id khi truyền từ server xuống
   * temp-randomStr khi tạo field mới
   */
  _id?: string;
  label: string;
  isPublic?: boolean;
  isRequired?: boolean;
  required?: boolean; // Alias for isRequired
  tooltip?: string;
  profilePermissions?: Array<{
    _id: string;
    name: string;
    permission: 'read_write' | 'read' | 'none';
  }>;

  // Properties từ LayoutField - cần thiết cho Dynamic Layout Builder
  /**
   * Loại field - sử dụng FieldType để đảm bảo type safety
   */
  type: FieldType;
  order?: number;
  description?: string; // Description for field type selector
  placeholder?: string; // Placeholder text for form fields

  // Additional properties for QuickCreate compatibility
  defaultValue?: FieldDefaultValue;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
  options?: FieldOptions;

  permissionProfiles: FieldPermissionProfile[];
}

export type Field =
  | TextField
  | EmailField
  | PhoneField
  | UrlField
  | TextareaField
  | NumberField
  | DecimalField
  | CurrencyField
  | PercentField
  | DateField
  | DateTimeField
  | PicklistField
  | MultiPicklistField
  | SearchField
  | UserField
  | UploadFileField
  | UploadImageField
  | CheckboxField;

export interface TextField extends BaseField {
  type: 'text';
  value?: FieldValue;
  constraints?: {
    maxLength?: number;
    unique?: boolean;
  };
}

export interface EmailField extends BaseField {
  type: 'email';
  value?: FieldValue;
  constraints?: {
    unique?: boolean;
  };
}

export interface PhoneField extends BaseField {
  type: 'phone';
  value?: FieldValue;
  constraints?: {
    maxLength?: number;
    maxDigits?: number;
    unique?: boolean;
  };
}

export interface UrlField extends BaseField {
  type: 'url';
  value?: FieldValue;
  constraints?: {
    maxLength?: number;
    unique?: boolean;
  };
}

export interface TextareaField extends BaseField {
  type: 'textarea';
  value?: FieldValue;
  constraints?: {
    textType: 'small' | 'large' | 'rich';
    maxLength: number;
  };
}

export interface NumberField extends BaseField {
  type: 'number';
  value?: FieldValue;
  constraints?: {
    maxDigits?: number;
  };
}

export interface DecimalField extends BaseField {
  type: 'decimal';
  value?: FieldValue;
  constraints?: {
    maxDigits?: number;
    decimalPlaces?: number;
    useNumberSeparator?: boolean;
  };
}

export interface CurrencyField extends BaseField {
  type: 'currency';
  value?: FieldValue;
  constraints?: {
    maxDigits?: number;
    decimalPlaces?: number;
    rounding?: 'normal' | 'off' | 'up' | 'down';
  };
}

export interface PercentField extends BaseField {
  type: 'percent';
  value?: FieldValue;
  constraints?: {};
}

export interface DateField extends BaseField {
  type: 'date';
  value?: FieldValue;
  constraints?: {};
}

export interface DateTimeField extends BaseField {
  type: 'datetime';
  value?: FieldValue;
  constraints?: {};
}

export interface PicklistField extends BaseField {
  type: 'picklist';
  value?: FieldValue;
  constraints?: {
    picklistValues: string[];
    sortOrder?: 'input' | 'alphabetical';
    defaultValue?: string;
  };
}

export interface MultiPicklistField extends BaseField {
  type: 'multi-picklist';
  value?: FieldValue;
  constraints?: {
    picklistValues: string[];
    sortOrder?: 'input' | 'alphabetical';
    defaultValue?: string[];
  };
}

export interface SearchField extends BaseField {
  type: 'search';
  value?: FieldValue;
  constraints?: {
    searchModule: 'sales_quotes' | 'contacts' | 'transactions';
  };
}

export interface UserField extends BaseField {
  type: 'user';
  value?: FieldValue;
  constraints?: {
    userType: 'single' | 'multiple';
  };
}

export interface UploadFileField extends BaseField {
  type: 'file';
  value?: FieldValue;
  constraints?: {
    allowMultipleFiles?: boolean;
    maxFiles?: number;
  };
}

export interface UploadImageField extends BaseField {
  type: 'image';
  value?: FieldValue;
  constraints?: {
    maxImages: number;
  };
}

export interface CheckboxField extends BaseField {
  type: 'checkbox';
  value?: FieldValue;
  constraints?: {
    enableByDefault?: boolean;
  };
}

