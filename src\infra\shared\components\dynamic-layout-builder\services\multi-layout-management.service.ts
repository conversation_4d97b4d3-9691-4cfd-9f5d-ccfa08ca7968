import { signal } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { delay, map } from 'rxjs/operators';

// Import interface mới từ dynamic-layout-config.model.ts
import { DynamicLayoutConfig } from '../models/dynamic-layout-config.model';
// Import DetailViewConfig từ DTO
import { DetailViewConfig } from '../models/dynamic-layout-builder.dto';
// Import các interface khác từ model cũ (giữ lại để tham khảo)
import {
  MultiLayoutConfig
} from '../models/dynamic-layout-builder.model';
import { FieldPermissionProfile } from '@domain/entities/field.entity';

/**
 * MultiLayoutManagementService - Dedicated service for multi-layout system management
 *
 * ✅ REFACTORED: Non-injectable service for multi-layout system management
 * ✅ CHANGED: Removed @Injectable() decorator - instantiate with 'new MultiLayoutManagementService()'
 *
 * **Responsibilities:**
 * - Manage multi-layout configuration và state
 * - Handle localStorage persistence for multi-layout data
 * - Provide observables và signals for multi-layout state
 * - Manage layout switching, creation, deletion operations
 * - Handle layout metadata và sorting
 *
 * **Architecture Separation:**
 * - DynamicLayoutBuilderService: Core layout building functionality
 * - LayoutSelectorService: High-level layout business logic và modal operations
 * - MultiLayoutManagementService: Multi-layout system management
 * - DynamicLayoutBuilderStateService: Centralized state management
 *
 * **Key Features:**
 * - localStorage persistence với automatic backup/restore
 * - Reactive state management với BehaviorSubjects và Signals
 * - Layout merging với original layouts
 * - Default layout management
 * - Multi-layout mode toggle
 *
 * **Architecture:**
 * - Non-injectable service với instance riêng cho mỗi component
 * - Reactive state với BehaviorSubjects và Observables
 * - localStorage persistence cho multi-layout configuration
 * - Error handling và logging
 *
 * **Usage:**
 * - Instantiate trong component: `private multiLayoutService = new MultiLayoutManagementService();`
 * - Mỗi component có instance riêng để tránh shared state conflicts
 */
export class MultiLayoutManagementService {
  private readonly MULTI_LAYOUT_STORAGE_KEY = 'dynamic-layout-builder-multi-config';

  // ==================== REACTIVE STATE MANAGEMENT ====================

  // BehaviorSubjects for multi-layout state
  private multiLayoutConfigSubject = new BehaviorSubject<MultiLayoutConfig | null>(null);
  private currentLayoutIdSubject = new BehaviorSubject<string>('');
  private availableLayoutsSubject = new BehaviorSubject<DynamicLayoutConfig[]>([]);

  // Observables for multi-layout state
  multiLayoutConfig$ = this.multiLayoutConfigSubject.asObservable();
  currentLayoutId$ = this.currentLayoutIdSubject.asObservable();
  availableLayouts$ = this.availableLayoutsSubject.asObservable();

  // Signals for multi-layout state
  multiLayoutConfig = signal<MultiLayoutConfig | null>(null);
  currentLayoutId = signal<string>('');
  availableLayouts = signal<DynamicLayoutConfig[]>([]);

  // ==================== INITIALIZATION METHODS ====================

  /**
   * Initialize multi-layout system
   * Loads saved config from localStorage hoặc creates default config
   */
  initializeMultiLayoutSystem(originalLayouts?: DynamicLayoutConfig[]): void {
    try {
      const savedConfig = localStorage.getItem(this.MULTI_LAYOUT_STORAGE_KEY);
      if (savedConfig) {
        let config: MultiLayoutConfig = JSON.parse(savedConfig);

        // Merge original layouts with saved layouts
        if (originalLayouts && originalLayouts.length > 0) {
          config = this.mergeOriginalLayouts(config, originalLayouts);
        }

        this.updateAllState(config);
        // console.log('✅ Multi-layout system initialized with merged layouts:', config);
      } else {
        // Create default multi-layout config with original layouts
        this.createDefaultMultiLayoutConfig(originalLayouts);
      }
    } catch (error) {
      console.error('❌ Error initializing multi-layout system:', error);
      this.createDefaultMultiLayoutConfig(originalLayouts);
    }
  }

  /**
   * Create default multi-layout configuration
   * Creates a default layout và adds any provided original layouts
   */
  private createDefaultMultiLayoutConfig(originalLayouts?: DynamicLayoutConfig[]): void {
    const defaultLayoutId = this.generateId();

    // Mock data cho defaultProfiles và fieldSettings
    const mockDefaultProfiles: FieldPermissionProfile[] = [
      { _id: 'admin', name: 'Administrator', permission: 'read_write' },
      { _id: 'manager', name: 'Manager', permission: 'read_write' },
      { _id: 'user', name: 'Standard User', permission: 'read' },
      { _id: 'guest', name: 'Guest User', permission: 'none' }
    ];

    const mockFieldSettings = {
      availableSearchModules: [
        { _id: 'sales_quotes', name: 'Sales Quotes' },
        { _id: 'contacts', name: 'Contacts' },
        { _id: 'transactions', name: 'Transactions' }
      ]
    };

    const defaultLayout: DynamicLayoutConfig = {
      _id: defaultLayoutId,
      title: 'Layout Mặc Định',
      name: 'Layout Mặc Định',
      shortDescription: 'Layout mặc định cho hệ thống',
      sections: [],
      enableQuickCreate: true,
      quickCreateConfig: this.getDefaultQuickCreateConfig(),
      detailViewConfig: this.getDefaultDetailViewConfig(),
      createdAt: new Date(),
      updatedAt: new Date(),
      fieldDefaultSettings: {
        permissionProfiles: mockDefaultProfiles,
        availableSearchModules: mockFieldSettings.availableSearchModules
      },
    };

    // Start with default layout - sử dụng DynamicLayoutConfig thay vì LayoutMetadata
    const layouts: { [key: string]: DynamicLayoutConfig } = { [defaultLayoutId]: defaultLayout };
    const metadata: { [key: string]: DynamicLayoutConfig } = { [defaultLayoutId]: defaultLayout };

    // Add original layouts if provided
    if (originalLayouts && originalLayouts.length > 0) {
      originalLayouts.forEach(originalLayout => {
        const layoutId = originalLayout._id || this.generateId();

        // Ensure layout has proper _id
        const layoutWithId = {
          ...originalLayout,
          _id: layoutId
        };

        layouts[layoutId] = layoutWithId;

        // Create metadata for original layout - sử dụng toàn bộ layout làm metadata
        metadata[layoutId] = {
          ...layoutWithId,
          name: originalLayout.title,
          createdAt: originalLayout.createdAt || new Date(),
          updatedAt: originalLayout.updatedAt || new Date(),
        };

        // console.log('📋 Added original layout to default config:', originalLayout.title);
      });
    }

    const config: MultiLayoutConfig = {
      layouts,
      metadata,
      currentLayoutId: defaultLayoutId,
      multiLayoutMode: false
    };

    this.saveAndUpdateState(config);
    // console.log('✅ Created default multi-layout config with', Object.keys(layouts).length, 'layouts');
  }

  // ==================== LAYOUT OPERATIONS ====================

  /**
   * Get all available layouts sorted by default first, then by creation date
   */
  getAllLayouts(): Observable<DynamicLayoutConfig[]> {
    const config = this.multiLayoutConfig();
    if (config) {
      const layouts = Object.values(config.metadata).sort((a, b) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
      return of(layouts);
    }
    return of([]);
  }

  /**
   * Switch to different layout
   * Updates current layout ID và returns the layout config
   */
  switchToLayout(layoutId: string): Observable<DynamicLayoutConfig> {
    const config = this.multiLayoutConfig();
    if (!config || !config.layouts[layoutId]) {
      throw new Error(`Layout with ID ${layoutId} not found`);
    }

    const layout = config.layouts[layoutId];

    // Update current layout ID
    const updatedConfig: MultiLayoutConfig = {
      ...config,
      currentLayoutId: layoutId
    };

    this.saveAndUpdateState(updatedConfig);
    // console.log('✅ Switched to layout:', layoutId, layout.title);
    return of(layout).pipe(delay(300));
  }

  /**
   * Add new layout to multi-layout config
   * Used by LayoutSelectorService after creating new layout
   */
  addLayout(layout: DynamicLayoutConfig, metadata: DynamicLayoutConfig): Observable<boolean> {
    const config = this.multiLayoutConfig();
    if (!config) {
      throw new Error('Multi-layout system not initialized');
    }

    const updatedConfig: MultiLayoutConfig = {
      ...config,
      layouts: { ...config.layouts, [layout._id!]: layout },
      metadata: { ...config.metadata, [metadata._id!]: metadata }
    };

    this.saveAndUpdateState(updatedConfig);
    // console.log('✅ Added new layout to multi-layout config:', layout.title);
    return of(true).pipe(delay(100));
  }

  /**
   * Update existing layout config và metadata
   */
  updateLayout(layoutId: string, updatedLayout: Partial<DynamicLayoutConfig>): Observable<boolean> {
    const config = this.multiLayoutConfig();
    if (!config || !config.layouts[layoutId]) {
      throw new Error(`Layout with ID ${layoutId} not found`);
    }

    const currentLayout = config.layouts[layoutId];
    const updatedLayoutFull: DynamicLayoutConfig = {
      ...currentLayout,
      ...updatedLayout
    };

    // Update metadata - sử dụng DynamicLayoutConfig thay vì LayoutMetadata
    const metadata = config.metadata[layoutId];
    const updatedMetadata: DynamicLayoutConfig = {
      ...metadata,
      ...updatedLayoutFull,
      updatedAt: new Date(),
    };

    const updatedConfig: MultiLayoutConfig = {
      ...config,
      layouts: { ...config.layouts, [layoutId]: updatedLayoutFull },
      metadata: { ...config.metadata, [layoutId]: updatedMetadata }
    };

    this.saveAndUpdateState(updatedConfig);
    // console.log('✅ Updated layout:', layoutId, updatedLayoutFull.title);
    return of(true).pipe(delay(100));
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Generate unique ID for layouts
   */
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Get default detail view config
   */
  private getDefaultDetailViewConfig() {
    return {
      title: 'Chi tiết',
      sections: [],
      layout: 'single-column' as const
    };
  }

  /**
   * Get default quick create config
   */
  private getDefaultQuickCreateConfig() {
    return {
      title: 'Quick Create',
      description: 'Tạo nhanh',
      section: {
        _id: this.generateId(),
        title: 'Thông tin cơ bản',
        fields: []
      },
      enableAutoSave: false
    };
  }

  /**
   * Save config to localStorage và update all reactive state
   */
  private saveAndUpdateState(config: MultiLayoutConfig): void {
    this.saveMultiLayoutConfig(config);
    this.updateAllState(config);
  }

  /**
   * Update all reactive state (subjects và signals)
   */
  private updateAllState(config: MultiLayoutConfig): void {
    this.multiLayoutConfigSubject.next(config);
    this.multiLayoutConfig.set(config);
    this.currentLayoutIdSubject.next(config.currentLayoutId);
    this.currentLayoutId.set(config.currentLayoutId);
    this.updateAvailableLayouts(config);
  }

  /**
   * Update available layouts signal với sorted layouts
   */
  private updateAvailableLayouts(config: MultiLayoutConfig): void {
    const layouts = Object.values(config.metadata).sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    this.availableLayouts.set(layouts);
    this.availableLayoutsSubject.next(layouts);
  }

  /**
   * Save multi-layout config to localStorage
   */
  private saveMultiLayoutConfig(config: MultiLayoutConfig): void {
    try {
      localStorage.setItem(this.MULTI_LAYOUT_STORAGE_KEY, JSON.stringify(config));
      // console.log('💾 Multi-layout config saved to localStorage');
    } catch (error) {
      console.error('❌ Error saving multi-layout config:', error);
    }
  }

  /**
   * Merge original layouts with saved multi-layout config
   * Ensures original layouts are preserved và updated
   */
  private mergeOriginalLayouts(savedConfig: MultiLayoutConfig, originalLayouts: DynamicLayoutConfig[]): MultiLayoutConfig {
    const mergedLayouts: { [key: string]: DynamicLayoutConfig } = { ...savedConfig.layouts };
    const mergedMetadata: { [key: string]: DynamicLayoutConfig } = { ...savedConfig.metadata };

    // Add original layouts that don't exist in saved config
    originalLayouts.forEach(originalLayout => {
      const layoutId = originalLayout._id || this.generateId();

      if (!mergedLayouts[layoutId]) {
        // Add new original layout
        const layoutWithMetadata: DynamicLayoutConfig = {
          ...originalLayout,
          _id: layoutId,
          name: originalLayout.title,
          createdAt: originalLayout.createdAt || new Date(),
          updatedAt: originalLayout.updatedAt || new Date(),
        };
        mergedLayouts[layoutId] = layoutWithMetadata;
        mergedMetadata[layoutId] = layoutWithMetadata;
        // console.log('📋 Added missing original layout:', originalLayout.title);
      } else {
        // Update existing layout với original data (preserve user changes in metadata)
        mergedLayouts[layoutId] = { ...originalLayout, _id: layoutId };
        // console.log('🔄 Updated existing layout with original data:', originalLayout.title);
      }
    });

    const mergedConfig: MultiLayoutConfig = {
      ...savedConfig,
      layouts: mergedLayouts,
      metadata: mergedMetadata
    };

    // Save merged config back to localStorage
    this.saveMultiLayoutConfig(mergedConfig);

    // console.log('✅ Merged original layouts with saved config:', Object.keys(mergedLayouts).length, 'total layouts');
    return mergedConfig;
  }

  // ==================== LAYOUT MANAGEMENT OPERATIONS ====================

  /**
   * Delete layout from multi-layout config
   * Cannot delete the last remaining layout
   */
  deleteLayout(layoutId: string): Observable<boolean> {
    const config = this.multiLayoutConfig();
    if (!config) {
      throw new Error('Multi-layout system not initialized');
    }

    const layoutCount = Object.keys(config.layouts).length;
    if (layoutCount <= 1) {
      throw new Error('Cannot delete the last remaining layout');
    }

    if (!config.layouts[layoutId]) {
      throw new Error(`Layout with ID ${layoutId} not found`);
    }

    // Remove layout và metadata
    const { [layoutId]: removedLayout, ...remainingLayouts } = config.layouts;
    const { [layoutId]: removedMetadata, ...remainingMetadata } = config.metadata;

    // If deleting current layout, switch to first available layout
    let newCurrentLayoutId = config.currentLayoutId;
    if (config.currentLayoutId === layoutId) {
      newCurrentLayoutId = Object.keys(remainingLayouts)[0];
    }

    const updatedConfig: MultiLayoutConfig = {
      ...config,
      layouts: remainingLayouts,
      metadata: remainingMetadata,
      currentLayoutId: newCurrentLayoutId
    };

    this.saveAndUpdateState(updatedConfig);
    // console.log('✅ Layout deleted:', layoutId, 'New current layout:', newCurrentLayoutId);
    return of(true).pipe(delay(300));
  }

  /**
   * Get current layout config
   */
  getCurrentLayout(): DynamicLayoutConfig | null {
    const config = this.multiLayoutConfig();
    const currentId = this.currentLayoutId();

    if (config && currentId && config.layouts[currentId]) {
      return config.layouts[currentId];
    }

    return null;
  }

  /**
   * Enable/disable multi-layout mode
   */
  setMultiLayoutMode(enabled: boolean): void {
    const config = this.multiLayoutConfig();
    if (config) {
      const updatedConfig: MultiLayoutConfig = {
        ...config,
        multiLayoutMode: enabled
      };
      this.saveAndUpdateState(updatedConfig);
      // console.log('✅ Multi-layout mode:', enabled ? 'enabled' : 'disabled');
    }
  }

  /**
   * Get layout by ID
   */
  getLayoutById(layoutId: string): DynamicLayoutConfig | null {
    const config = this.multiLayoutConfig();
    if (config && config.layouts[layoutId]) {
      return config.layouts[layoutId];
    }
    return null;
  }

  /**
   * Check if layout exists
   */
  layoutExists(layoutId: string): boolean {
    const config = this.multiLayoutConfig();
    return config ? !!config.layouts[layoutId] : false;
  }

  /**
   * Get layout metadata by ID
   */
  getLayoutMetadata(layoutId: string): DynamicLayoutConfig | null {
    const config = this.multiLayoutConfig();
    if (config && config.metadata[layoutId]) {
      return config.metadata[layoutId];
    }
    return null;
  }

  /**
   * Update layout metadata only
   */
  updateLayoutMetadata(layoutId: string, metadata: Partial<DynamicLayoutConfig>): Observable<boolean> {
    const config = this.multiLayoutConfig();
    if (!config || !config.metadata[layoutId]) {
      throw new Error(`Layout metadata with ID ${layoutId} not found`);
    }

    const updatedMetadata: DynamicLayoutConfig = {
      ...config.metadata[layoutId],
      ...metadata,
      updatedAt: new Date()
    };

    const updatedConfig: MultiLayoutConfig = {
      ...config,
      metadata: { ...config.metadata, [layoutId]: updatedMetadata }
    };

    this.saveAndUpdateState(updatedConfig);
    // console.log('✅ Updated layout metadata:', layoutId);
    return of(true).pipe(delay(100));
  }

  /**
   * Clear all layouts và reset to default
   * WARNING: This will remove all user-created layouts
   */
  resetToDefault(): void {
    localStorage.removeItem(this.MULTI_LAYOUT_STORAGE_KEY);
    this.createDefaultMultiLayoutConfig();
    // console.log('⚠️ Multi-layout system reset to default');
  }

  /**
   * Export multi-layout config for backup
   */
  exportConfig(): MultiLayoutConfig | null {
    return this.multiLayoutConfig();
  }

  /**
   * Import multi-layout config from backup
   */
  importConfig(config: MultiLayoutConfig): void {
    this.saveAndUpdateState(config);
    // console.log('✅ Multi-layout config imported');
  }

  // ==================== BUSINESS LOGIC METHODS (Moved from LayoutSelectorService) ====================

  /**
   * Create new layout - High-level business logic
   * ✅ MOVED FROM LayoutSelectorService: Centralized multi-layout business logic
   * Coordinates between validation, creation, cloning, và state sync
   */
  createNewLayoutWithBusinessLogic(name: string, description?: string, templateLayout?: DynamicLayoutConfig): Observable<string> {
    // Ensure service is initialized
    this.ensureInitialized();

    const newLayoutId = this.generateId();
    const now = new Date();

    // Mock data cho defaultProfiles và fieldSettings
    const mockDefaultProfiles: FieldPermissionProfile[] = [
      { _id: 'admin', name: 'Administrator', permission: 'read_write' },
      { _id: 'manager', name: 'Manager', permission: 'read_write' },
      { _id: 'user', name: 'Standard User', permission: 'read' },
      { _id: 'guest', name: 'Guest User', permission: 'none' }
    ];

    const mockFieldSettings = {
      availableSearchModules: [
        { _id: 'sales_quotes', name: 'Sales Quotes' },
        { _id: 'contacts', name: 'Contacts' },
        { _id: 'transactions', name: 'Transactions' }
      ]
    };

    // Create new layout based on template or default
    const newLayout: DynamicLayoutConfig = templateLayout ?
      this.cloneLayoutWithNewIds(templateLayout, newLayoutId, name, description) : {
      _id: newLayoutId,
      title: name,
      shortDescription: description,
      sections: [],
      enableQuickCreate: true,
      quickCreateConfig: this.getDefaultQuickCreateConfig(),
      detailViewConfig: this.getDefaultDetailViewConfig(),
      createdAt: now,
      updatedAt: now,
      fieldDefaultSettings: {
        permissionProfiles: mockDefaultProfiles,
        availableSearchModules: mockFieldSettings.availableSearchModules
      },
    };

    const newLayoutWithMetadata: DynamicLayoutConfig = {
      ...newLayout,
      _id: newLayoutId,
      name,
      createdAt: now,
      updatedAt: now,
    };

    // Add layout to multi-layout config
    // console.log('🚀 MultiLayoutManagementService: Creating new layout with business logic');

    return this.addLayout(newLayout, newLayoutWithMetadata).pipe(
      map(() => {
        // console.log('✅ New layout created via MultiLayoutManagementService business logic:', newLayoutId, newLayout.title);
        return newLayoutId;
      })
    );
  }

  /**
   * Clone layout with new IDs for all sections and fields
   * ✅ MOVED FROM LayoutSelectorService: Enhanced version with full compatibility
   * Used by createNewLayoutWithBusinessLogic for template-based layout creation
   */
  private cloneLayoutWithNewIds(
    templateLayout: DynamicLayoutConfig,
    newLayoutId: string,
    name: string,
    description?: string
  ): DynamicLayoutConfig {
    // Deep clone the template layout
    const clonedLayout: DynamicLayoutConfig = JSON.parse(JSON.stringify(templateLayout));

    // Update basic properties
    clonedLayout._id = newLayoutId;
    clonedLayout.title = name;
    clonedLayout.shortDescription = description;

    // Generate new IDs for all sections
    if (clonedLayout.sections) {
      clonedLayout.sections = clonedLayout.sections.map(section => ({
        ...section,
        _id: this.generateId(),
        id: this.generateId(),
        // Generate new IDs for all fields in this section
        fields: section.fields.map(field => ({
          ...field,
          _id: this.generateId(),
          id: Math.floor(Math.random() * 1000000) // Generate random number for backward compatibility
        }))
      }));
    }

    // Update Quick Create config if exists
    if (clonedLayout.quickCreateConfig?.section) {
      clonedLayout.quickCreateConfig.section = {
        ...clonedLayout.quickCreateConfig.section,
        _id: this.generateId(),
        fields: clonedLayout.quickCreateConfig.section.fields.map(field => ({
          ...field,
          _id: this.generateId(),
          id: Math.floor(Math.random() * 1000000) // Generate random number for backward compatibility
        }))
      };
    }

    // console.log('🔄 MultiLayoutManagementService: Cloned layout with new IDs:', {
    //   _id: newLayoutId,
    //   title: name,
    //   shortDescription: description,
    //   originalSections: templateLayout.sections?.length || 0,
    //   clonedSections: clonedLayout.sections?.length || 0
    // });

    return clonedLayout;
  }

  /**
   * Ensure MultiLayoutManagementService is initialized
   * ✅ MOVED FROM LayoutSelectorService: Centralized initialization logic
   */
  private ensureInitialized(): void {
    const currentConfig = this.multiLayoutConfig();
    if (!currentConfig) {
      // console.log('🚀 MultiLayoutManagementService: Auto-initializing service');
      this.initializeMultiLayoutSystem();
    }
  }

  // ==================== CLEANUP METHODS ====================

  /**
   * Cleanup service khi component bị destroy
   * ✅ NEW: Memory management để tránh memory leaks
   */
  public destroy(): void {
    // console.log('🧹 MultiLayoutManagementService: Cleanup service');

    // Complete tất cả BehaviorSubjects để tránh memory leaks
    this.multiLayoutConfigSubject.complete();
    this.currentLayoutIdSubject.complete();
    this.availableLayoutsSubject.complete();
  }
}
